PORT=8000
DB_HOST=localhost
DB_PORT=5432
DB_USER=admin
DB_PASS=admin123
DATABASE_NAME=pgrooms
DATABASE_URL=postgresql://${DB_USER}:${DB_PASS}@${DB_HOST}:${DB_PORT}/${DATABASE_NAME}
JWT_SECRET_KEY=AUcLQK49Jd_LuRHL8w4a4HU0aQy7YI_1tjuzR9ANAMRA-Ji_TFpfqH_rxkAJ7NFCKAM

# Email Configuration (Gmail)
GMAIL_USER_MAIL=<EMAIL>
GMAIL_APP_PASSWORD=your_gmail_app_password

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587

# AWS KEYS
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_BUCKET_REGION=your-region
AWS_BUCKET_NAME=your-bucket-name

# Razorpay Configuration
# Test Environment
RAZORPAY_KEY_ID=rzp_test_xxxxxxxxxx
RAZORPAY_KEY_SECRET=your_razorpay_test_secret_key
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_key

# Production Environment (uncomment for production)
# RAZORPAY_KEY_ID=rzp_live_xxxxxxxxxx
# RAZORPAY_KEY_SECRET=your_razorpay_live_secret_key
# RAZORPAY_WEBHOOK_SECRET=your_live_webhook_secret_key