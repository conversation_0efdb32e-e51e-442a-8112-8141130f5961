{"name": "backend", "version": "1.0.0", "main": "app/index.js", "scripts": {"start": "nodemon app/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@prisma/client": "^6.5.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "pg": "^8.13.1", "prisma-pagination": "^0.2.3", "razorpay": "^2.9.6", "uuid": "^11.1.0", "winston": "^3.17.0"}, "prisma": {"schema": "prisma/schema.prisma"}, "devDependencies": {"prisma": "^6.5.0"}}