const express = require("express");
const router = express.Router();
const controller = require("../controllers/index");
const validators = require("../validators/index");
const validateRequest = require("../middleware/ValidationMiddleware");
const {
  uploadImages,
  validateFileUpload,
} = require("../middleware/MulterMiddleware");

/**
 * Property Routes
 */
router
  .route("/property")
  .post(
    uploadImages,
    validateFileUpload,
    validateRequest(validators.PropertyValidator),
    controller.PropertyController.addProperty
  )
  .put(
    uploadImages,
    validateFileUpload,
    validateRequest(validators.PropertyValidator),
    controller.PropertyController.updateProperty
  );

router
  .route("/property/:id")
  .get(controller.PropertyController.getProperty)
  .delete(controller.PropertyController.deleteProperty);

router.post("/properties", controller.PropertyController.getAllProperties);

router.put(
  "/propertyStatus",
  validateRequest(validators.PropertyStatusValidator),
  controller.PropertyController.updatePropertyStatus
);

/**
 * ROOM ROUTES
 */

router
  .route("/room")
  .post(
    uploadImages,
    validateFileUpload,
    validateRequest(validators.RoomValidator),
    controller.RoomController.addRoom
  )
  .put(
    uploadImages,
    validateFileUpload,
    validateRequest(validators.RoomValidator),
    controller.RoomController.updateRoom
  );

router.post("/rooms", controller.RoomController.getAllRooms);

router
  .route("/room/:id")
  .get(controller.RoomController.getRoom)
  .delete(controller.RoomController.deleteRoom);

/**
 * USER ROUTES
 */
router.post("/getTenants", controller.UserController.getTenants);

/**
 * TENANT ROUTES
 */
router
  .route("/tenant")
  .post(
    validateRequest(validators.TenantValidator),
    controller.TenantController.createTenant
  )
  .put(
    validateRequest(validators.TenantValidator),
    controller.TenantController.updateTenant
)
  .get(
    controller.TenantController.getTenants
  );


  /**
   * DASHBOARD ROUTES
   */
router.get("/dashboard-monitoring-cards", controller.DashboardController.getMonitoringCards);
  router.get("/dashboard-recent-tenants", controller.DashboardController.getRecentTenants);

/**
 * PAYMENT ROUTES
 */
router
  .route("/payment/create-order")
  .post(
    validateRequest(validators.PaymentValidator.CreatePaymentOrderValidator),
    controller.PaymentController.createPaymentOrder
  );

router
  .route("/payment/verify")
  .post(
    validateRequest(validators.PaymentValidator.VerifyPaymentValidator),
    controller.PaymentController.verifyPayment
  );

router
  .route("/payment/list")
  .post(
    validateRequest(validators.PaymentValidator.PaymentListValidator),
    controller.PaymentController.getAllPayments
  );

router
  .route("/payment/tenant")
  .post(
    validateRequest(validators.PaymentValidator.TenantPaymentsValidator),
    controller.PaymentController.getPaymentsByTenant
  );

router
  .route("/payment/property")
  .post(
    validateRequest(validators.PaymentValidator.PropertyPaymentsValidator),
    controller.PaymentController.getPaymentsByProperty
  );

router
  .route("/payment/refund")
  .post(
    validateRequest(validators.PaymentValidator.RefundValidator),
    controller.PaymentController.initiateRefund
  );

router
  .route("/payment/cancel")
  .post(
    validateRequest(validators.PaymentValidator.CancelPaymentValidator),
    controller.PaymentController.cancelPayment
  );

// Specific payment routes (must come before parameterized routes)
router.get("/payment/stats", controller.PaymentController.getPaymentStats);
router.get("/payment/recent", controller.PaymentController.getRecentPayments);
router.get("/payment/analytics/monthly", controller.PaymentController.getMonthlyAnalytics);

// Parameterized payment routes (must come after specific routes)
router
  .route("/payment/:id")
  .get(controller.PaymentController.getPaymentById);

module.exports = router;
