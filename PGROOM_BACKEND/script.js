/**
 * 
INSERT INTO public."State" (id, "stateName", "updatedAt", "createdAt") 
VALUES 
(DEFAULT, 'Andhra Pradesh', NOW(), NOW()),
(DEFAULT, 'Arunachal Pradesh', NOW(), NOW()),
(DEFAULT, 'Assam', NOW(), NOW()),
(DEFAULT, 'Bihar', NOW(), NOW()),
(DEFAULT, 'Chhattisgarh', NOW(), NOW()),
(DEFAULT, 'Goa', NOW(), NOW()),
(DEFAULT, 'Gujarat', NOW(), NOW()),
(DEFAULT, 'Haryana', NOW(), NOW()),
(DEFAULT, 'Himachal Pradesh', NOW(), NOW()),
(DEFAULT, 'Jharkhand', NOW(), NOW()),
(DEFAULT, 'Karnataka', NOW(), NOW()),
(DEFAULT, 'Kerala', NOW(), NOW()),
(DEFAULT, 'Madhya Pradesh', NOW(), NOW()),
(DEFAULT, 'Maharashtra', NOW(), NOW()),
(DEFAULT, 'Manipur', NOW(), NOW()),
(DEFAULT, 'Meghalaya', NOW(), NOW()),
(DEFAULT, 'Mizoram', NOW(), NOW()),
(DEFAULT, 'Nagaland', NOW(), NOW()),
(DEFAULT, 'Odisha', NOW(), NOW()),
(DEFAULT, 'Punjab', NOW(), NOW()),
(DEFAULT, 'Rajasthan', NOW(), NOW()),
(DEFAULT, 'Sikkim', NOW(), NOW()),
(DEFAULT, 'Tamil Nadu', NOW(), NOW()),
(DEFAULT, 'Telangana', NOW(), NOW()),
(DEFAULT, 'Tripura', NOW(), NOW()),
(DEFAULT, 'Uttar Pradesh', NOW(), NOW()),
(DEFAULT, 'Uttarakhand', NOW(), NOW()),
(DEFAULT, 'West Bengal', NOW(), NOW());

INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 1, 'Addanki', NOW(), NOW()),
    (DEFAULT, 1, 'Addatigala', NOW(), NOW()),
    (DEFAULT, 1, 'Adoni', NOW(), NOW()),
    (DEFAULT, 1, 'Ahobilam', NOW(), NOW()),
    (DEFAULT, 1, 'Ainavolu', NOW(), NOW()),
    (DEFAULT, 1, 'Alampur', NOW(), NOW()),
    (DEFAULT, 1, 'Alipur', NOW(), NOW()),
    (DEFAULT, 1, 'Allagadda', NOW(), NOW()),
    (DEFAULT, 1, 'Alur', NOW(), NOW()),
    (DEFAULT, 1, 'Amalapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Amaravati', NOW(), NOW()),
    (DEFAULT, 1, 'Ambur', NOW(), NOW()),
    (DEFAULT, 1, 'Anakapalle', NOW(), NOW()),
    (DEFAULT, 1, 'Anantapur', NOW(), NOW()),
    (DEFAULT, 1, 'Annavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Araku Valley', NOW(), NOW()),
    (DEFAULT, 1, 'Atmakur', NOW(), NOW()),
    (DEFAULT, 1, 'Atmakuru', NOW(), NOW()),
    (DEFAULT, 1, 'Avanigadda', NOW(), NOW()),
    (DEFAULT, 1, 'Ayirāla', NOW(), NOW()),
    (DEFAULT, 1, 'Badvel', NOW(), NOW()),
    (DEFAULT, 1, 'Bagepalli', NOW(), NOW()),
    (DEFAULT, 1, 'Ballari', NOW(), NOW()),
    (DEFAULT, 1, 'Banganapalli', NOW(), NOW()),
    (DEFAULT, 1, 'Bangarapet', NOW(), NOW()),
    (DEFAULT, 1, 'Bapatla', NOW(), NOW()),
    (DEFAULT, 1, 'Benz Circle', NOW(), NOW()),
    (DEFAULT, 1, 'Bhadrāchalam', NOW(), NOW()),
    (DEFAULT, 1, 'Bheemili', NOW(), NOW()),
    (DEFAULT, 1, 'Bheemulavaripalem', NOW(), NOW()),
    (DEFAULT, 1, 'Bhimavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Bhimunipatnam', NOW(), NOW()),
    (DEFAULT, 1, 'Bhogopuram', NOW(), NOW()),
    (DEFAULT, 1, 'Bobbili', NOW(), NOW()),
    (DEFAULT, 1, 'Brahmapur', NOW(), NOW()),
    (DEFAULT, 1, 'Buckinghampet', NOW(), NOW()),
    (DEFAULT, 1, 'Challakere', NOW(), NOW()),
    (DEFAULT, 1, 'Chandragiri', NOW(), NOW()),
    (DEFAULT, 1, 'Chintalapudi', NOW(), NOW()),
    (DEFAULT, 1, 'Chintamani', NOW(), NOW()),
    (DEFAULT, 1, 'Chintapalle', NOW(), NOW()),
    (DEFAULT, 1, 'Chipurupalle', NOW(), NOW()),
    (DEFAULT, 1, 'Chirala', NOW(), NOW()),
    (DEFAULT, 1, 'Chittoor', NOW(), NOW()),
    (DEFAULT, 1, 'Chodavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Darsi', NOW(), NOW()),
    (DEFAULT, 1, 'Dharmavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Dhone', NOW(), NOW()),
    (DEFAULT, 1, 'Dindi', NOW(), NOW()),
    (DEFAULT, 1, 'Draksharama', NOW(), NOW()),
    (DEFAULT, 1, 'Dumbriguda', NOW(), NOW()),
    (DEFAULT, 1, 'Dwaraka Tirumala', NOW(), NOW()),
    (DEFAULT, 1, 'Eluru', NOW(), NOW()),
    (DEFAULT, 1, 'Gajapatinagaram', NOW(), NOW()),
    (DEFAULT, 1, 'Gandikota', NOW(), NOW()),
    (DEFAULT, 1, 'Gannavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Gauribidanur', NOW(), NOW()),
    (DEFAULT, 1, 'Giddalur', NOW(), NOW()),
    (DEFAULT, 1, 'Gooty', NOW(), NOW()),
    (DEFAULT, 1, 'Gudibanda', NOW(), NOW()),
    (DEFAULT, 1, 'Gudivada', NOW(), NOW()),
    (DEFAULT, 1, 'Gudiyatham', NOW(), NOW()),
    (DEFAULT, 1, 'Gudur', NOW(), NOW()),
    (DEFAULT, 1, 'Gummidipundi', NOW(), NOW()),
    (DEFAULT, 1, 'Guntakal', NOW(), NOW()),
    (DEFAULT, 1, 'Guntur', NOW(), NOW()),
    (DEFAULT, 1, 'Gunupur', NOW(), NOW()),
    (DEFAULT, 1, 'Guruzala', NOW(), NOW()),
    (DEFAULT, 1, 'Hindupur', NOW(), NOW()),
    (DEFAULT, 1, 'Hiriyur', NOW(), NOW()),
    (DEFAULT, 1, 'Horsley Hills', NOW(), NOW()),
    (DEFAULT, 1, 'Ichchapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Irukkam Island', NOW(), NOW()),
    (DEFAULT, 1, 'Jaggayyapeta', NOW(), NOW()),
    (DEFAULT, 1, 'Jammalamadugu', NOW(), NOW()),
    (DEFAULT, 1, 'Jangareddigudem', NOW(), NOW()),
    (DEFAULT, 1, 'Kadapa', NOW(), NOW()),
    (DEFAULT, 1, 'Kadiri', NOW(), NOW()),
    (DEFAULT, 1, 'Kaikalur', NOW(), NOW()),
    (DEFAULT, 1, 'Kakinada', NOW(), NOW()),
    (DEFAULT, 1, 'Kalyandurg', NOW(), NOW()),
    (DEFAULT, 1, 'Kamalapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Kanakammachattram', NOW(), NOW()),
    (DEFAULT, 1, 'Kandukur', NOW(), NOW()),
    (DEFAULT, 1, 'Kanigiri', NOW(), NOW()),
    (DEFAULT, 1, 'Kanipakam', NOW(), NOW()),
    (DEFAULT, 1, 'Katpadi', NOW(), NOW()),
    (DEFAULT, 1, 'Kavali', NOW(), NOW()),
    (DEFAULT, 1, 'Kavutaram', NOW(), NOW()),
    (DEFAULT, 1, 'Koilkuntla', NOW(), NOW()),
    (DEFAULT, 1, 'Kolhapur', NOW(), NOW()),
    (DEFAULT, 1, 'Kondapalle', NOW(), NOW()),
    (DEFAULT, 1, 'Konta', NOW(), NOW()),
    (DEFAULT, 1, 'Kottapeta', NOW(), NOW()),
    (DEFAULT, 1, 'Kovvur', NOW(), NOW()),
    (DEFAULT, 1, 'Koyyalagūdem', NOW(), NOW()),
    (DEFAULT, 1, 'Krishnagiri', NOW(), NOW()),
    (DEFAULT, 1, 'Krīshnāpatnam', NOW(), NOW()),
    (DEFAULT, 1, 'Kuppam', NOW(), NOW()),
    (DEFAULT, 1, 'Kurnool', NOW(), NOW()),
    (DEFAULT, 1, 'Lambasingi', NOW(), NOW()),
    (DEFAULT, 1, 'Lepakshi', NOW(), NOW()),
    (DEFAULT, 1, 'Machilipatnam', NOW(), NOW()),
    (DEFAULT, 1, 'Madakasira', NOW(), NOW()),
    (DEFAULT, 1, 'Madanapalle', NOW(), NOW()),
    (DEFAULT, 1, 'Madhira', NOW(), NOW()),
    (DEFAULT, 1, 'Madhugiri', NOW(), NOW()),
    (DEFAULT, 1, 'Madkar', NOW(), NOW()),
    (DEFAULT, 1, 'Malakanagiri', NOW(), NOW()),
    (DEFAULT, 1, 'Mandapeta', NOW(), NOW()),
    (DEFAULT, 1, 'Mangalagiri', NOW(), NOW()),
    (DEFAULT, 1, 'Mantralayam', NOW(), NOW()),
    (DEFAULT, 1, 'Manvi', NOW(), NOW()),
    (DEFAULT, 1, 'Mardagūda', NOW(), NOW()),
    (DEFAULT, 1, 'Maredumilli', NOW(), NOW()),
    (DEFAULT, 1, 'Markapur', NOW(), NOW()),
    (DEFAULT, 1, 'Molakalmuru', NOW(), NOW()),
    (DEFAULT, 1, 'Mopidevi', NOW(), NOW()),
    (DEFAULT, 1, 'Mulapadava', NOW(), NOW()),
    (DEFAULT, 1, 'Mulbagal', NOW(), NOW()),
    (DEFAULT, 1, 'Mummidivaram', NOW(), NOW()),
    (DEFAULT, 1, 'Muttukadu', NOW(), NOW()),
    (DEFAULT, 1, 'Nagulaupalapadu', NOW(), NOW()),
    (DEFAULT, 1, 'Nandigama', NOW(), NOW()),
    (DEFAULT, 1, 'Nandikotkur', NOW(), NOW()),
    (DEFAULT, 1, 'Nandyal', NOW(), NOW()),
    (DEFAULT, 1, 'Narasaraopet', NOW(), NOW()),
    (DEFAULT, 1, 'Narsannapeta', NOW(), NOW()),
    (DEFAULT, 1, 'Narsapur', NOW(), NOW()),
    (DEFAULT, 1, 'Narsipatnam', NOW(), NOW()),
    (DEFAULT, 1, 'Nellore', NOW(), NOW()),
    (DEFAULT, 1, 'Nidadavolu', NOW(), NOW()),
    (DEFAULT, 1, 'Nuzvid', NOW(), NOW()),
    (DEFAULT, 1, 'Ongole', NOW(), NOW()),
    (DEFAULT, 1, 'Paderu', NOW(), NOW()),
    (DEFAULT, 1, 'Palakollu', NOW(), NOW()),
    (DEFAULT, 1, 'Palakonda', NOW(), NOW()),
    (DEFAULT, 1, 'Pallippattu', NOW(), NOW()),
    (DEFAULT, 1, 'Peddapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Penukonda', NOW(), NOW()),
    (DEFAULT, 1, 'Phirangipuram', NOW(), NOW()),
    (DEFAULT, 1, 'Pithapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Prakasam', NOW(), NOW()),
    (DEFAULT, 1, 'Puttur', NOW(), NOW()),
    (DEFAULT, 1, 'Peddapalli', NOW(), NOW()),
    (DEFAULT, 1, 'Rajahmundry', NOW(), NOW()),
    (DEFAULT, 1, 'Rajam', NOW(), NOW()),
    (DEFAULT, 1, 'Ramachandrapuram', NOW(), NOW()),
    (DEFAULT, 1, 'Raptadu', NOW(), NOW()),
    (DEFAULT, 1, 'Rayachoti', NOW(), NOW()),
    (DEFAULT, 1, 'Rayadurg', NOW(), NOW()),
    (DEFAULT, 1, 'Rekalapudi', NOW(), NOW()),
    (DEFAULT, 1, 'Revanapet', NOW(), NOW()),
    (DEFAULT, 1, 'Rural', NOW(), NOW()),
	    (DEFAULT, 1, 'Sankavaram', NOW(), NOW()),
    (DEFAULT, 1, 'Satyavada', NOW(), NOW()),
    (DEFAULT, 1, 'Siddipet', NOW(), NOW()),
    (DEFAULT, 1, 'Srikalahasti', NOW(), NOW()),
    (DEFAULT, 1, 'Srikakulam', NOW(), NOW()),
    (DEFAULT, 1, 'Srinivaspur', NOW(), NOW()),
    (DEFAULT, 1, 'Tadpatri', NOW(), NOW()),
    (DEFAULT, 1, 'Tadepalligudem', NOW(), NOW()),
    (DEFAULT, 1, 'Tirupati', NOW(), NOW()),
    (DEFAULT, 1, 'Tuni', NOW(), NOW()),
    (DEFAULT, 1, 'Uppal', NOW(), NOW()),
    (DEFAULT, 1, 'Uravakonda', NOW(), NOW()),
    (DEFAULT, 1, 'Vemulawada', NOW(), NOW()),
    (DEFAULT, 1, 'Vijayawada', NOW(), NOW()),
    (DEFAULT, 1, 'Vijayanagaram', NOW(), NOW()),
    (DEFAULT, 1, 'Visakhapatnam', NOW(), NOW()),
    (DEFAULT, 1, 'Vizianagaram', NOW(), NOW()),
    (DEFAULT, 1, 'Vuyyuru', NOW(), NOW()),
    (DEFAULT, 1, 'Warangal', NOW(), NOW()),
    (DEFAULT, 1, 'Yadagirigutta', NOW(), NOW()),
    (DEFAULT, 1, 'Yemmiganur', NOW(), NOW()),
    (DEFAULT, 1, 'Yerpedu', NOW(), NOW());

INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 3, 'Guwahati', NOW(), NOW()),
    (DEFAULT, 3, 'Dibrugarh', NOW(), NOW()),
    (DEFAULT, 3, 'Jorhat', NOW(), NOW()),
    (DEFAULT, 3, 'Nagaon', NOW(), NOW()),
    (DEFAULT, 3, 'Silchar', NOW(), NOW()),
    (DEFAULT, 3, 'Tinsukia', NOW(), NOW()),
    (DEFAULT, 3, 'Tezpur', NOW(), NOW()),
    (DEFAULT, 3, 'Sivasagar', NOW(), NOW()),
    (DEFAULT, 3, 'Bongaigaon', NOW(), NOW()),
    (DEFAULT, 3, 'Barpeta', NOW(), NOW()),
    (DEFAULT, 3, 'Karimganj', NOW(), NOW()),
    (DEFAULT, 3, 'Nalbari', NOW(), NOW()),
    (DEFAULT, 3, 'Golaghat', NOW(), NOW()),
    (DEFAULT, 3, 'Lakhimpur', NOW(), NOW()),
    (DEFAULT, 3, 'Dhemaji', NOW(), NOW()),
    (DEFAULT, 3, 'Dhubri', NOW(), NOW()),
    (DEFAULT, 3, 'Hailakandi', NOW(), NOW()),
    (DEFAULT, 3, 'Majuli', NOW(), NOW()),
    (DEFAULT, 3, 'Baska', NOW(), NOW()),
    (DEFAULT, 3, 'Morigaon', NOW(), NOW());

    INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 4, 'Patna', NOW(), NOW()),
    (DEFAULT, 4, 'Gaya', NOW(), NOW()),
    (DEFAULT, 4, 'Bhagalpur', NOW(), NOW()),
    (DEFAULT, 4, 'Muzaffarpur', NOW(), NOW()),
    (DEFAULT, 4, 'Purnia', NOW(), NOW()),
    (DEFAULT, 4, 'Darbhanga', NOW(), NOW()),
    (DEFAULT, 4, 'Begusarai', NOW(), NOW()),
    (DEFAULT, 4, 'Munger', NOW(), NOW()),
    (DEFAULT, 4, 'Samastipur', NOW(), NOW()),
    (DEFAULT, 4, 'Arrah', NOW(), NOW()),
    (DEFAULT, 4, 'Bihar Sharif', NOW(), NOW()),
    (DEFAULT, 4, 'Chapra', NOW(), NOW()),
    (DEFAULT, 4, 'Siwan', NOW(), NOW()),
    (DEFAULT, 4, 'Saharsa', NOW(), NOW()),
    (DEFAULT, 4, 'Jamui', NOW(), NOW()),
    (DEFAULT, 4, 'Katihar', NOW(), NOW()),
    (DEFAULT, 4, 'Nawada', NOW(), NOW()),
    (DEFAULT, 4, 'Saran', NOW(), NOW()),
    (DEFAULT, 4, 'Motihari', NOW(), NOW()),
    (DEFAULT, 4, 'Madhubani', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 5, 'Raipur', NOW(), NOW()),
    (DEFAULT, 5, 'Bilaspur', NOW(), NOW()),
    (DEFAULT, 5, 'Durg', NOW(), NOW()),
    (DEFAULT, 5, 'Korba', NOW(), NOW()),
    (DEFAULT, 5, 'Bhilai', NOW(), NOW()),
    (DEFAULT, 5, 'Raigarh', NOW(), NOW()),
    (DEFAULT, 5, 'Jagdalpur', NOW(), NOW()),
    (DEFAULT, 5, 'Ambikapur', NOW(), NOW()),
    (DEFAULT, 5, 'Kawardha', NOW(), NOW()),
    (DEFAULT, 5, 'Janjgir', NOW(), NOW()),
    (DEFAULT, 5, 'Mahasamund', NOW(), NOW()),
    (DEFAULT, 5, 'Dantewada', NOW(), NOW()),
    (DEFAULT, 5, 'Bastar', NOW(), NOW()),
    (DEFAULT, 5, 'Rajnandgaon', NOW(), NOW()),
    (DEFAULT, 5, 'Sukma', NOW(), NOW()),
    (DEFAULT, 5, 'Kanker', NOW(), NOW()),
    (DEFAULT, 5, 'Narayanpur', NOW(), NOW()),
    (DEFAULT, 5, 'Kondagaon', NOW(), NOW()),
    (DEFAULT, 5, 'Surajpur', NOW(), NOW()),
    (DEFAULT, 5, 'Balod', NOW(), NOW()),
    (DEFAULT, 5, 'Mungeli', NOW(), NOW()),
    (DEFAULT, 5, 'Bemetara', NOW(), NOW()),
    (DEFAULT, 5, 'Pendra', NOW(), NOW()),
    (DEFAULT, 5, 'Korba', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 6, 'Panaji', NOW(), NOW()),
    (DEFAULT, 6, 'Margao', NOW(), NOW()),
    (DEFAULT, 6, 'Vasco da Gama', NOW(), NOW()),
    (DEFAULT, 6, 'Mapusa', NOW(), NOW()),
    (DEFAULT, 6, 'Ponda', NOW(), NOW()),
    (DEFAULT, 6, 'Bicholim', NOW(), NOW()),
    (DEFAULT, 6, 'Quepem', NOW(), NOW()),
    (DEFAULT, 6, 'Curchorem', NOW(), NOW()),
    (DEFAULT, 6, 'Sanguem', NOW(), NOW()),
    (DEFAULT, 6, 'Dharbandora', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 7, 'Ahmedabad', NOW(), NOW()),
    (DEFAULT, 7, 'Surat', NOW(), NOW()),
    (DEFAULT, 7, 'Vadodara', NOW(), NOW()),
    (DEFAULT, 7, 'Rajkot', NOW(), NOW()),
    (DEFAULT, 7, 'Bhavnagar', NOW(), NOW()),
    (DEFAULT, 7, 'Jamnagar', NOW(), NOW()),
    (DEFAULT, 7, 'Junagadh', NOW(), NOW()),
    (DEFAULT, 7, 'Gandhinagar', NOW(), NOW()),
    (DEFAULT, 7, 'Anand', NOW(), NOW()),
    (DEFAULT, 7, 'Nadiad', NOW(), NOW()),
    (DEFAULT, 7, 'Valsad', NOW(), NOW()),
    (DEFAULT, 7, 'Mehsana', NOW(), NOW()),
    (DEFAULT, 7, 'Bharuch', NOW(), NOW()),
    (DEFAULT, 7, 'Morbi', NOW(), NOW()),
    (DEFAULT, 7, 'Porbandar', NOW(), NOW()),
    (DEFAULT, 7, 'Navsari', NOW(), NOW()),
    (DEFAULT, 7, 'Gandhidham', NOW(), NOW()),
    (DEFAULT, 7, 'Dahod', NOW(), NOW()),
    (DEFAULT, 7, 'Patan', NOW(), NOW()),
    (DEFAULT, 7, 'Vapi', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 8, 'Ambala', NOW(), NOW()),
    (DEFAULT, 8, 'Bhiwani', NOW(), NOW()),
    (DEFAULT, 8, 'Faridabad', NOW(), NOW()),
    (DEFAULT, 8, 'Fatehabad', NOW(), NOW()),
    (DEFAULT, 8, 'Gurugram', NOW(), NOW()),
    (DEFAULT, 8, 'Hisar', NOW(), NOW()),
    (DEFAULT, 8, 'Jind', NOW(), NOW()),
    (DEFAULT, 8, 'Kaithal', NOW(), NOW()),
    (DEFAULT, 8, 'Karnal', NOW(), NOW()),
    (DEFAULT, 8, 'Kurukshetra', NOW(), NOW()),
    (DEFAULT, 8, 'Mahendragarh', NOW(), NOW()),
    (DEFAULT, 8, 'Narnaul', NOW(), NOW()),
    (DEFAULT, 8, 'Palwal', NOW(), NOW()),
    (DEFAULT, 8, 'Panchkula', NOW(), NOW()),
    (DEFAULT, 8, 'Panipat', NOW(), NOW()),
    (DEFAULT, 8, 'Rewari', NOW(), NOW()),
    (DEFAULT, 8, 'Rohtak', NOW(), NOW()),
    (DEFAULT, 8, 'Sirsa', NOW(), NOW()),
    (DEFAULT, 8, 'Sonipat', NOW(), NOW()),
    (DEFAULT, 8, 'Yamunanagar', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 9, 'Bilaspur', NOW(), NOW()),
    (DEFAULT, 9, 'Chamba', NOW(), NOW()),
    (DEFAULT, 9, 'Dharamsala', NOW(), NOW()),
    (DEFAULT, 9, 'Hamirpur', NOW(), NOW()),
    (DEFAULT, 9, 'Kullu', NOW(), NOW()),
    (DEFAULT, 9, 'Mandi', NOW(), NOW()),
    (DEFAULT, 9, 'Nahan', NOW(), NOW()),
    (DEFAULT, 9, 'Solan', NOW(), NOW()),
    (DEFAULT, 9, 'Sirmaur', NOW(), NOW()),
    (DEFAULT, 9, 'Kangra', NOW(), NOW()),
    (DEFAULT, 9, 'Kasauli', NOW(), NOW()),
    (DEFAULT, 9, 'Una', NOW(), NOW()),
    (DEFAULT, 9, 'Palampur', NOW(), NOW()),
    (DEFAULT, 9, 'Reckong Peo', NOW(), NOW()),
    (DEFAULT, 9, 'Rohru', NOW(), NOW()),
    (DEFAULT, 9, 'Sundernagar', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 10, 'Bokaro Steel City', NOW(), NOW()),
    (DEFAULT, 10, 'Dhanbad', NOW(), NOW()),
    (DEFAULT, 10, 'Jamshedpur', NOW(), NOW()),
    (DEFAULT, 10, 'Ranchi', NOW(), NOW()),
    (DEFAULT, 10, 'Hazaribagh', NOW(), NOW()),
    (DEFAULT, 10, 'Giridih', NOW(), NOW()),
    (DEFAULT, 10, 'Deoghar', NOW(), NOW()),
    (DEFAULT, 10, 'Chaibasa', NOW(), NOW()),
    (DEFAULT, 10, 'Madhupur', NOW(), NOW()),
    (DEFAULT, 10, 'Khunti', NOW(), NOW()),
    (DEFAULT, 10, 'Jhumri Telaiya', NOW(), NOW()),
    (DEFAULT, 10, 'Ramgarh', NOW(), NOW()),
    (DEFAULT, 10, 'Pakur', NOW(), NOW()),
    (DEFAULT, 10, 'Lohardaga', NOW(), NOW()),
    (DEFAULT, 10, 'Koderma', NOW(), NOW()),
    (DEFAULT, 10, 'Simdega', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 11, 'Bengaluru', NOW(), NOW()),
    (DEFAULT, 11, 'Mysuru', NOW(), NOW()),
    (DEFAULT, 11, 'Hubballi', NOW(), NOW()),
    (DEFAULT, 11, 'Dharwad', NOW(), NOW()),
    (DEFAULT, 11, 'Mangaluru', NOW(), NOW()),
    (DEFAULT, 11, 'Belagavi', NOW(), NOW()),
    (DEFAULT, 11, 'Ballari', NOW(), NOW()),
    (DEFAULT, 11, 'Tumakuru', NOW(), NOW()),
    (DEFAULT, 11, 'Shimoga', NOW(), NOW()),
    (DEFAULT, 11, 'Raichur', NOW(), NOW()),
    (DEFAULT, 11, 'Chitradurga', NOW(), NOW()),
    (DEFAULT, 11, 'Kolar', NOW(), NOW()),
    (DEFAULT, 11, 'Bagalkot', NOW(), NOW()),
    (DEFAULT, 11, 'Hassan', NOW(), NOW()),
    (DEFAULT, 11, 'Chikkamagaluru', NOW(), NOW()),
    (DEFAULT, 11, 'Karwar', NOW(), NOW()),
    (DEFAULT, 11, 'Mandya', NOW(), NOW()),
    (DEFAULT, 11, 'Bidar', NOW(), NOW()),
    (DEFAULT, 11, 'Gulbarga', NOW(), NOW()),
    (DEFAULT, 11, 'Raichur', NOW(), NOW()),
    (DEFAULT, 11, 'Udupi', NOW(), NOW()),
    (DEFAULT, 11, 'Koppal', NOW(), NOW()),
    (DEFAULT, 11, 'Yadgir', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 12, 'Alappuzha', NOW(), NOW()),
    (DEFAULT, 12, 'Ernakulam', NOW(), NOW()),
    (DEFAULT, 12, 'Kottayam', NOW(), NOW()),
    (DEFAULT, 12, 'Kochi', NOW(), NOW()),
    (DEFAULT, 12, 'Thiruvananthapuram', NOW(), NOW()),
    (DEFAULT, 12, 'Kozhikode', NOW(), NOW()),
    (DEFAULT, 12, 'Thrissur', NOW(), NOW()),
    (DEFAULT, 12, 'Malappuram', NOW(), NOW()),
    (DEFAULT, 12, 'Kannur', NOW(), NOW()),
    (DEFAULT, 12, 'Palakkad', NOW(), NOW()),
    (DEFAULT, 12, 'Pathanamthitta', NOW(), NOW()),
    (DEFAULT, 12, 'Idukki', NOW(), NOW()),
    (DEFAULT, 12, 'Wayanad', NOW(), NOW()),
    (DEFAULT, 12, 'Kasargod', NOW(), NOW()),
    (DEFAULT, 12, 'Muvattupuzha', NOW(), NOW()),
    (DEFAULT, 12, 'Punalur', NOW(), NOW()),
    (DEFAULT, 12, 'Varkala', NOW(), NOW()),
    (DEFAULT, 12, 'Changanassery', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 13, 'Bhopal', NOW(), NOW()),
    (DEFAULT, 13, 'Indore', NOW(), NOW()),
    (DEFAULT, 13, 'Gwalior', NOW(), NOW()),
    (DEFAULT, 13, 'Jabalpur', NOW(), NOW()),
    (DEFAULT, 13, 'Ujjain', NOW(), NOW()),
    (DEFAULT, 13, 'Sagar', NOW(), NOW()),
    (DEFAULT, 13, 'Satna', NOW(), NOW()),
    (DEFAULT, 13, 'Rewa', NOW(), NOW()),
    (DEFAULT, 13, 'Khandwa', NOW(), NOW()),
    (DEFAULT, 13, 'Dewas', NOW(), NOW()),
    (DEFAULT, 13, 'Ratlam', NOW(), NOW()),
    (DEFAULT, 13, 'Shivpuri', NOW(), NOW()),
    (DEFAULT, 13, 'Chhindwara', NOW(), NOW()),
    (DEFAULT, 13, 'Kolkata', NOW(), NOW()),
    (DEFAULT, 13, 'Mandsaur', NOW(), NOW()),
    (DEFAULT, 13, 'Vidisha', NOW(), NOW()),
    (DEFAULT, 13, 'Neemuch', NOW(), NOW()),
    (DEFAULT, 13, 'Bhind', NOW(), NOW()),
    (DEFAULT, 13, 'Shahdol', NOW(), NOW()),
    (DEFAULT, 13, 'Balaghat', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 14, 'Mumbai', NOW(), NOW()),
    (DEFAULT, 14, 'Pune', NOW(), NOW()),
    (DEFAULT, 14, 'Nagpur', NOW(), NOW()),
    (DEFAULT, 14, 'Nashik', NOW(), NOW()),
    (DEFAULT, 14, 'Aurangabad', NOW(), NOW()),
    (DEFAULT, 14, 'Solapur', NOW(), NOW()),
    (DEFAULT, 14, 'Thane', NOW(), NOW()),
    (DEFAULT, 14, 'Satara', NOW(), NOW()),
    (DEFAULT, 14, 'Kolhapur', NOW(), NOW()),
    (DEFAULT, 14, 'Chandrapur', NOW(), NOW()),
    (DEFAULT, 14, 'Amravati', NOW(), NOW()),
    (DEFAULT, 14, 'Jalna', NOW(), NOW()),
    (DEFAULT, 14, 'Wardha', NOW(), NOW()),
    (DEFAULT, 14, 'Bhiwandi', NOW(), NOW()),
    (DEFAULT, 14, 'Navi Mumbai', NOW(), NOW()),
    (DEFAULT, 14, 'Latur', NOW(), NOW()),
    (DEFAULT, 14, 'Shirdi', NOW(), NOW()),
    (DEFAULT, 14, 'Dhule', NOW(), NOW()),
    (DEFAULT, 14, 'Kalyan', NOW(), NOW()),
    (DEFAULT, 14, 'Kochi', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 15, 'Imphal', NOW(), NOW()),
    (DEFAULT, 15, 'Churachandpur', NOW(), NOW()),
    (DEFAULT, 15, 'Thoubal', NOW(), NOW()),
    (DEFAULT, 15, 'Kakching', NOW(), NOW()),
    (DEFAULT, 15, 'Bishnupur', NOW(), NOW()),
    (DEFAULT, 15, 'Jiribam', NOW(), NOW()),
    (DEFAULT, 15, 'Senapati', NOW(), NOW()),
    (DEFAULT, 15, 'Ukhrul', NOW(), NOW()),
    (DEFAULT, 15, 'Tamenglong', NOW(), NOW()),
    (DEFAULT, 15, 'Chandel', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 16, 'Shillong', NOW(), NOW()),
    (DEFAULT, 16, 'Tura', NOW(), NOW()),
    (DEFAULT, 16, 'Nongstoin', NOW(), NOW()),
    (DEFAULT, 16, 'Jowai', NOW(), NOW()),
    (DEFAULT, 16, 'Williamnagar', NOW(), NOW()),
    (DEFAULT, 16, 'Baghmara', NOW(), NOW()),
    (DEFAULT, 16, 'Mairang', NOW(), NOW()),
    (DEFAULT, 16, 'Resubelpara', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 18, 'Dimapur', NOW(), NOW()),
    (DEFAULT, 18, 'Kohima', NOW(), NOW()),
    (DEFAULT, 18, 'Mokokchung', NOW(), NOW()),
    (DEFAULT, 18, 'Mon', NOW(), NOW()),
    (DEFAULT, 18, 'Peren', NOW(), NOW()),
    (DEFAULT, 18, 'Zunheboto', NOW(), NOW()),
    (DEFAULT, 18, 'Tuensang', NOW(), NOW()),
    (DEFAULT, 18, 'Wokha', NOW(), NOW()),
    (DEFAULT, 18, 'Phek', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 19, 'Bhubaneswar', NOW(), NOW()),
    (DEFAULT, 19, 'Cuttack', NOW(), NOW()),
    (DEFAULT, 19, 'Berhampur', NOW(), NOW()),
    (DEFAULT, 19, 'Rourkela', NOW(), NOW()),
    (DEFAULT, 19, 'Puri', NOW(), NOW()),
    (DEFAULT, 19, 'Sambalpur', NOW(), NOW()),
    (DEFAULT, 19, 'Balasore', NOW(), NOW()),
    (DEFAULT, 19, 'Koraput', NOW(), NOW()),
    (DEFAULT, 19, 'Bargarh', NOW(), NOW()),
    (DEFAULT, 19, 'Dhenkanal', NOW(), NOW()),
    (DEFAULT, 19, 'Angul', NOW(), NOW()),
    (DEFAULT, 19, 'Jharsuguda', NOW(), NOW()),
    (DEFAULT, 19, 'Kendrapara', NOW(), NOW()),
    (DEFAULT, 19, 'Bhadrak', NOW(), NOW()),
    (DEFAULT, 19, 'Nayagarh', NOW(), NOW()),
    (DEFAULT, 19, 'Kalahandi', NOW(), NOW()),
    (DEFAULT, 19, 'Ganjam', NOW(), NOW()),
    (DEFAULT, 19, 'Malkangiri', NOW(), NOW()),
    (DEFAULT, 19, 'Nuapada', NOW(), NOW()),
    (DEFAULT, 19, 'Rayagada', NOW(), NOW()),
    (DEFAULT, 19, 'Mayurbhanj', NOW(), NOW()),
    (DEFAULT, 19, 'Keonjhar', NOW(), NOW()),
    (DEFAULT, 19, 'Jagatsinghpur', NOW(), NOW()),
    (DEFAULT, 19, 'Kendujhar', NOW(), NOW()),
    (DEFAULT, 19, 'Gajapati', NOW(), NOW()),
    (DEFAULT, 19, 'Boudh', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 20, 'Amritsar', NOW(), NOW()),
    (DEFAULT, 20, 'Bathinda', NOW(), NOW()),
    (DEFAULT, 20, 'Chandigarh', NOW(), NOW()),
    (DEFAULT, 20, 'Firozpur', NOW(), NOW()),
    (DEFAULT, 20, 'Hoshiarpur', NOW(), NOW()),
    (DEFAULT, 20, 'Jalandhar', NOW(), NOW()),
    (DEFAULT, 20, 'Kapurthala', NOW(), NOW()),
    (DEFAULT, 20, 'Ludhiana', NOW(), NOW()),
    (DEFAULT, 20, 'Mansa', NOW(), NOW()),
    (DEFAULT, 20, 'Moga', NOW(), NOW()),
    (DEFAULT, 20, 'Pathankot', NOW(), NOW()),
    (DEFAULT, 20, 'Patiala', NOW(), NOW()),
    (DEFAULT, 20, 'Rupnagar', NOW(), NOW()),
    (DEFAULT, 20, 'Sangrur', NOW(), NOW()),
    (DEFAULT, 20, 'Shaheed Bhagat Singh Nagar', NOW(), NOW()),
    (DEFAULT, 20, 'Sri Muktsar Sahib', NOW(), NOW()),
    (DEFAULT, 20, 'Tarn Taran', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 21, 'Ajmer', NOW(), NOW()),
    (DEFAULT, 21, 'Alwar', NOW(), NOW()),
    (DEFAULT, 21, 'Bikaner', NOW(), NOW()),
    (DEFAULT, 21, 'Churu', NOW(), NOW()),
    (DEFAULT, 21, 'Dausa', NOW(), NOW()),
    (DEFAULT, 21, 'Jaisalmer', NOW(), NOW()),
    (DEFAULT, 21, 'Jaipur', NOW(), NOW()),
    (DEFAULT, 21, 'Jodhpur', NOW(), NOW()),
    (DEFAULT, 21, 'Kota', NOW(), NOW()),
    (DEFAULT, 21, 'Pali', NOW(), NOW()),
    (DEFAULT, 21, 'Sikar', NOW(), NOW()),
    (DEFAULT, 21, 'Tonk', NOW(), NOW()),
    (DEFAULT, 21, 'Udaipur', NOW(), NOW()),
    (DEFAULT, 21, 'Barmer', NOW(), NOW()),
    (DEFAULT, 21, 'Bundi', NOW(), NOW()),
    (DEFAULT, 21, 'Chittorgarh', NOW(), NOW()),
    (DEFAULT, 21, 'Sawai Madhopur', NOW(), NOW()),
    (DEFAULT, 21, 'Banswara', NOW(), NOW()),
    (DEFAULT, 21, 'Dungarpur', NOW(), NOW()),
    (DEFAULT, 21, 'Rishabhdeo', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 22, 'Gangtok', NOW(), NOW()),
    (DEFAULT, 22, 'Namchi', NOW(), NOW()),
    (DEFAULT, 22, 'Pakyong', NOW(), NOW()),
    (DEFAULT, 22, 'Mangan', NOW(), NOW()),
    (DEFAULT, 22, 'Jorethang', NOW(), NOW()),
    (DEFAULT, 22, 'Rongli', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 23, 'Chennai', NOW(), NOW()),
    (DEFAULT, 23, 'Coimbatore', NOW(), NOW()),
    (DEFAULT, 23, 'Madurai', NOW(), NOW()),
    (DEFAULT, 23, 'Trichy', NOW(), NOW()),
    (DEFAULT, 23, 'Salem', NOW(), NOW()),
    (DEFAULT, 23, 'Tirunelveli', NOW(), NOW()),
    (DEFAULT, 23, 'Erode', NOW(), NOW()),
    (DEFAULT, 23, 'Vellore', NOW(), NOW()),
    (DEFAULT, 23, 'Thanjavur', NOW(), NOW()),
    (DEFAULT, 23, 'Tiruppur', NOW(), NOW()),
    (DEFAULT, 23, 'Kanchipuram', NOW(), NOW()),
    (DEFAULT, 23, 'Thoothukudi', NOW(), NOW()),
    (DEFAULT, 23, 'Nagercoil', NOW(), NOW()),
    (DEFAULT, 23, 'Arakkonam', NOW(), NOW()),
    (DEFAULT, 23, 'Cuddalore', NOW(), NOW()),
    (DEFAULT, 23, 'Karur', NOW(), NOW()),
    (DEFAULT, 23, 'Dindigul', NOW(), NOW()),
    (DEFAULT, 23, 'Ramanathapuram', NOW(), NOW()),
    (DEFAULT, 23, 'Kumbakonam', NOW(), NOW()),
    (DEFAULT, 23, 'Chidambaram', NOW(), NOW()),
    (DEFAULT, 23, 'Tiruvarur', NOW(), NOW()),
    (DEFAULT, 23, 'Pudukkottai', NOW(), NOW()),
    (DEFAULT, 23, 'Ariyalur', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 24, 'Adilabad', NOW(), NOW()),
    (DEFAULT, 24, 'Hyderabad', NOW(), NOW()),
    (DEFAULT, 24, 'Karimnagar', NOW(), NOW()),
    (DEFAULT, 24, 'Khammam', NOW(), NOW()),
    (DEFAULT, 24, 'Mahabubnagar', NOW(), NOW()),
    (DEFAULT, 24, 'Mancherial', NOW(), NOW()),
    (DEFAULT, 24, 'Medak', NOW(), NOW()),
    (DEFAULT, 24, 'Nalgonda', NOW(), NOW()),
    (DEFAULT, 24, 'Nizamabad', NOW(), NOW()),
    (DEFAULT, 24, 'Warangal', NOW(), NOW()),
    (DEFAULT, 24, 'Bhongir', NOW(), NOW()),
    (DEFAULT, 24, 'Jagitial', NOW(), NOW()),
    (DEFAULT, 24, 'Jangaon', NOW(), NOW()),
    (DEFAULT, 24, 'Mahabubabad', NOW(), NOW()),
    (DEFAULT, 24, 'Medchal', NOW(), NOW()),
    (DEFAULT, 24, 'Suryapet', NOW(), NOW()),
    (DEFAULT, 24, 'Peddapalli', NOW(), NOW()),
    (DEFAULT, 24, 'Rajanna Sircilla', NOW(), NOW()),
    (DEFAULT, 24, 'Jayashankar Bhupalapally', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 25, 'Agartala', NOW(), NOW()),
    (DEFAULT, 25, 'Ambassa', NOW(), NOW()),
    (DEFAULT, 25, 'Kailashahar', NOW(), NOW()),
    (DEFAULT, 25, 'Udaipur', NOW(), NOW()),
    (DEFAULT, 25, 'Sabroom', NOW(), NOW()),
    (DEFAULT, 25, 'Belonia', NOW(), NOW()),
    (DEFAULT, 25, 'Dharmanagar', NOW(), NOW()),
    (DEFAULT, 25, 'Khowai', NOW(), NOW()),
    (DEFAULT, 25, 'Mungiakami', NOW(), NOW()),
    (DEFAULT, 25, 'Radhakishorepur', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 26, 'Agra', NOW(), NOW()),
    (DEFAULT, 26, 'Aligarh', NOW(), NOW()),
    (DEFAULT, 26, 'Allahabad', NOW(), NOW()),
    (DEFAULT, 26, 'Bareilly', NOW(), NOW()),
    (DEFAULT, 26, 'Ghaziabad', NOW(), NOW()),
    (DEFAULT, 26, 'Gorakhpur', NOW(), NOW()),
    (DEFAULT, 26, 'Jhansi', NOW(), NOW()),
    (DEFAULT, 26, 'Kanpur', NOW(), NOW()),
    (DEFAULT, 26, 'Lucknow', NOW(), NOW()),
    (DEFAULT, 26, 'Mathura', NOW(), NOW()),
    (DEFAULT, 26, 'Meerut', NOW(), NOW()),
    (DEFAULT, 26, 'Moradabad', NOW(), NOW()),
    (DEFAULT, 26, 'Noida', NOW(), NOW()),
    (DEFAULT, 26, 'Varanasi', NOW(), NOW()),
    (DEFAULT, 26, 'Firozabad', NOW(), NOW()),
    (DEFAULT, 26, 'Raebareli', NOW(), NOW()),
    (DEFAULT, 26, 'Saharanpur', NOW(), NOW()),
    (DEFAULT, 26, 'Muzaffarnagar', NOW(), NOW()),
    (DEFAULT, 26, 'Agrahar', NOW(), NOW()),
    (DEFAULT, 26, 'Budaun', NOW(), NOW()),
    (DEFAULT, 26, 'Amroha', NOW(), NOW()),
    (DEFAULT, 26, 'Etawah', NOW(), NOW()),
    (DEFAULT, 26, 'Shahjahanpur', NOW(), NOW()),
    (DEFAULT, 26, 'Bijnor', NOW(), NOW()),
    (DEFAULT, 26, 'Ballia', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 27, 'Almora', NOW(), NOW()),
    (DEFAULT, 27, 'Dehradun', NOW(), NOW()),
    (DEFAULT, 27, 'Haridwar', NOW(), NOW()),
    (DEFAULT, 27, 'Nainital', NOW(), NOW()),
    (DEFAULT, 27, 'Rishikesh', NOW(), NOW()),
    (DEFAULT, 27, 'Roorkee', NOW(), NOW()),
    (DEFAULT, 27, 'Haldwani', NOW(), NOW()),
    (DEFAULT, 27, 'Kashipur', NOW(), NOW()),
    (DEFAULT, 27, 'Pauri', NOW(), NOW()),
    (DEFAULT, 27, 'Ramnagar', NOW(), NOW()),
    (DEFAULT, 27, 'Kotdwar', NOW(), NOW()),
    (DEFAULT, 27, 'Pantnagar', NOW(), NOW()),
    (DEFAULT, 27, 'Lansdowne', NOW(), NOW()),
    (DEFAULT, 27, 'Bhowali', NOW(), NOW()),
    (DEFAULT, 27, 'Bageshwar', NOW(), NOW());
INSERT INTO public."City"(
    id, "stateId", "cityName", "updatedAt", "createdAt")
VALUES
    (DEFAULT, 28, 'Asansol', NOW(), NOW()),
    (DEFAULT, 28, 'Bardhaman', NOW(), NOW()),
    (DEFAULT, 28, 'Kolkata', NOW(), NOW()),
    (DEFAULT, 28, 'Siliguri', NOW(), NOW()),
    (DEFAULT, 28, 'Durgapur', NOW(), NOW()),
    (DEFAULT, 28, 'Howrah', NOW(), NOW()),
    (DEFAULT, 28, 'Medinipur', NOW(), NOW()),
    (DEFAULT, 28, 'Bishnupur', NOW(), NOW()),
    (DEFAULT, 28, 'Cooch Behar', NOW(), NOW()),
    (DEFAULT, 28, 'Jalpaiguri', NOW(), NOW()),
    (DEFAULT, 28, 'Purulia', NOW(), NOW()),
    (DEFAULT, 28, 'Balarampur', NOW(), NOW()),
    (DEFAULT, 28, 'Bankura', NOW(), NOW()),
    (DEFAULT, 28, 'Krishnanagar', NOW(), NOW()),
    (DEFAULT, 28, 'Raiganj', NOW(), NOW()),
    (DEFAULT, 28, 'Suri', NOW(), NOW()),
    (DEFAULT, 28, 'Chandannagar', NOW(), NOW()),
    (DEFAULT, 28, 'Habra', NOW(), NOW()),
    (DEFAULT, 28, 'Tamluk', NOW(), NOW()),
    (DEFAULT, 28, 'Nadia', NOW(), NOW()),
    (DEFAULT, 28, 'Barasat', NOW(), NOW());
    
INSERT INTO public."UserRole"(
	id, roles, "createdAt", "updatedAt")
VALUES
    (1, 'SuperAdmin', NOW(), NOW()),
    (2, 'Admin', NOW(), NOW()),
    (3, 'Tenant', NOW(), NOW());


 */