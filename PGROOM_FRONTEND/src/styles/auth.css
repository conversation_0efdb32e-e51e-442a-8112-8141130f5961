/* 3D Flip Animation Styles */

/* Perspective container */
.perspective {
  perspective: 1000px;
}

/* Flip card container */
.flip-card {
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Inner card that will be flipped */
.flip-card-inner {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Preserve 3D for children */
.preserve-3d {
  transform-style: preserve-3d;
}

/* Hide backface of elements */
.backface-hidden {
  backface-visibility: hidden;
}

/* Rotation classes */
.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Accessibility - hide content visually but keep it accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
