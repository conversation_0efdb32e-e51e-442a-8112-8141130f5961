@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import toast styling */
@import './styles/toast.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 9% 12%;

    --card: 0 0% 100%;
    --card-foreground: 220 9% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 9% 12%;

    --primary: 142 76% 36%;
    --primary-foreground: 355 100% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    --chart-green: 142 84% 54%;
  }

  .dark {
    --background: 0 0% 11%; /* rgb(28, 25, 23) - Dark background */
    --foreground: 0 0% 98%; /* Light text */
    --background-light-dark: 0 0% 15%;

    --card: 0 0% 13%; /* Slightly lighter than background for cards */
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 13%;
    --popover-foreground: 0 0% 98%;

    --primary: 142 84% 54%; /* Bright green for primary elements */
    --primary-foreground: 0 0% 13%;

    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --accent: 142 84% 54%;
    --accent-foreground: 0 0% 13%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 142 84% 54%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased transition-colors duration-300;
  }

  /* Fix for browser autocomplete styling */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active,
  input[data-autocomplete-theme="true"]:-webkit-autofill,
  input[data-autocomplete-theme="true"]:-webkit-autofill:hover,
  input[data-autocomplete-theme="true"]:-webkit-autofill:focus,
  input[data-autocomplete-theme="true"]:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: hsl(var(--foreground));
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px hsl(var(--background));
    caret-color: hsl(var(--foreground));
  }

  .dark input:-webkit-autofill,
  .dark input:-webkit-autofill:hover,
  .dark input:-webkit-autofill:focus,
  .dark input:-webkit-autofill:active,
  .dark input[data-autocomplete-theme="true"]:-webkit-autofill,
  .dark input[data-autocomplete-theme="true"]:-webkit-autofill:hover,
  .dark input[data-autocomplete-theme="true"]:-webkit-autofill:focus,
  .dark input[data-autocomplete-theme="true"]:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: hsl(var(--foreground));
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px hsl(var(--background));
    caret-color: hsl(var(--foreground));
  }

  /* Firefox specific autocomplete styling */
  input:autofill,
  input[data-autocomplete-theme="true"]:autofill {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }

  /* Additional styling for form inputs with autocomplete */
  input:-webkit-autofill::first-line,
  input[data-autocomplete-theme="true"]:-webkit-autofill::first-line {
    color: hsl(var(--foreground));
    font-size: 0.875rem;
    font-family: inherit;
  }

  /* Ensure icons maintain their color when autocomplete is active */
  .relative:has(input:-webkit-autofill) svg,
  .relative:has(input[data-autocomplete-theme="true"]:-webkit-autofill) svg {
    color: hsl(var(--muted-foreground));
  }
}

@layer utilities {
  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .perspective {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .flip-card {
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .flip-card-inner {
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

